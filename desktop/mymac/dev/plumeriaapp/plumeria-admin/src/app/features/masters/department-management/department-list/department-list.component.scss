@import 'styles/variables';
@import 'styles/mixins';

.department-list-container {
  @include card-container;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: $text-color;
    }
  }

  mat-card {
    @include card-styling;
    background-color: $card-background;
  }

  .filter-container {
    margin-bottom: $spacing-lg;
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-md;

    .search-field {
      flex: 1;
      min-width: 250px;

      mat-form-field {
        width: 100%;
      }
    }
  }

  .action-buttons {
    margin: $spacing-lg 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions {
      display: flex;
      align-items: center;
      gap: $spacing-md;

      .show-inactive-toggle {
        margin-left: $spacing-md;
      }
    }
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl 0;

    p {
      margin-top: $spacing-md;
      color: $text-secondary;
    }
  }

  .error-message {
    @include message-box($error-color);
  }

  .success-message {
    @include message-box($success-color);
  }

  .no-data-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl 0;
    color: $text-secondary;

    mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: $spacing-md;
      opacity: 0.5;
    }

    p {
      font-size: 16px;
    }
  }

  .table-container {
    @include table-container;
    background-color: $card-background;
    border-radius: $card-border-radius;
    overflow: hidden;

    // Table styling improvements
    table {
      width: 100%;

      th, td {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
      }

      th {
        background-color: #fafafa;
        font-weight: 600;
        color: #333;
        font-size: 14px;
      }

      td {
        font-size: 14px;
        color: #555;
      }

      tr:hover {
        background-color: #f8f9fa;
      }
    }

    .mat-column-select {
      width: 60px;
      padding-right: $spacing-sm;
    }

    .mat-column-id {
      width: 60px;
      text-align: center;
    }

    .mat-column-code {
      width: 100px;
      font-family: 'Courier New', monospace;
      font-weight: 500;
    }

    .mat-column-status {
      width: 100px;
    }

    .mat-column-actions {
      width: 200px;
      text-align: right;
      padding-right: 16px;

      button {
        margin-left: 6px;
        width: 36px;
        height: 36px;
        border-radius: 6px;
        transition: all 0.2s ease;

        &:first-child {
          margin-left: 0;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }

        // Status toggle button
        &:first-child {
          mat-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
          }
        }

        // View button
        &:nth-child(2) {
          color: #2196f3;

          &:hover {
            background-color: rgba(33, 150, 243, 0.1);
          }
        }

        // Edit button
        &:nth-child(3) {
          color: #ff9800;

          &:hover {
            background-color: rgba(255, 152, 0, 0.1);
          }
        }

        // Delete button
        &:nth-child(4) {
          color: #f44336;

          &:hover {
            background-color: rgba(244, 67, 54, 0.1);
          }
        }
      }
    }
  }

  mat-paginator {
    margin-top: 0;
  }

  .status-active {
    color: $success-color;
    font-weight: 500;
  }

  .status-inactive {
    color: $warning-color;
    font-weight: 500;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .department-list-container {
    .filter-container {
      flex-direction: column;

      .search-field {
        width: 100%;
      }
    }

    .action-buttons {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;

      .left-actions {
        flex-wrap: wrap;
      }
    }

    .table-container {
      overflow-x: auto;
    }
  }
}
