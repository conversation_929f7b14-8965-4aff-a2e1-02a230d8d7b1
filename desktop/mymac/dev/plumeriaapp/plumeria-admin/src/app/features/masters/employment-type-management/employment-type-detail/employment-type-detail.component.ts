import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';
import {
  EmploymentTypeService,
  EmploymentType,
} from '../../../../core/services/masters/employment-type.service';

@Component({
  selector: 'app-employment-type-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatDividerModule,
    MatSnackBarModule,
    MatDialogModule,
  ],
  templateUrl: './employment-type-detail.component.html',
  styleUrls: ['./employment-type-detail.component.scss'],
})
export class EmploymentTypeDetailComponent implements OnInit {
  employmentType: EmploymentType | null = null;
  isLoading = false;
  errorMessage = '';
  employmentTypeId: number | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private employmentTypeService: EmploymentTypeService
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.employmentTypeId = +id;
        this.loadEmploymentType(this.employmentTypeId);
      } else {
        this.errorMessage = 'Employment Type ID not provided';
        this.isLoading = false;
      }
    });
  }

  loadEmploymentType(id: number): void {
    this.isLoading = true;
    this.employmentTypeService.getEmploymentTypeById(id).subscribe({
      next: (employmentType) => {
        this.employmentType = employmentType;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load employment type: ${error.message}`;
        this.isLoading = false;
      },
    });
  }

  editEmploymentType(): void {
    if (this.employmentTypeId) {
      this.router.navigate(['../edit', this.employmentTypeId], {
        relativeTo: this.route,
      });
    }
  }

  deleteEmploymentType(): void {
    if (!this.employmentType) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the employment type "${this.employmentType.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && this.employmentType) {
        this.employmentTypeService
          .deleteEmploymentType(this.employmentType.id!)
          .subscribe({
            next: () => {
              this.showSnackBar(
                `Employment Type ${this.employmentType?.name} deleted successfully`
              );
              this.router.navigate(['../../'], { relativeTo: this.route });
            },
            error: (error) => {
              this.showSnackBar(`Error: ${error.message}`, true);
            },
          });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
