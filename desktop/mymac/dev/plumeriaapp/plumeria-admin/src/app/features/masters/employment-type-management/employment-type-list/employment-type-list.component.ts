import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule, MatTable } from '@angular/material/table';
import {
  MatPaginatorModule,
  MatPaginator,
  PageEvent,
} from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';
import {
  EmploymentTypeService,
  EmploymentType,
} from '../../../../core/services/masters/employment-type.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-employment-type-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  templateUrl: './employment-type-list.component.html',
  styleUrls: ['./employment-type-list.component.scss'],
})
export class EmploymentTypeListComponent implements OnInit {
  employmentTypes: EmploymentType[] = [];
  filteredEmploymentTypes: EmploymentType[] = [];
  displayedEmploymentTypes: EmploymentType[] = [];
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<EmploymentType>(true, []);
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';
  includeInactive = true; // Set to true to include inactive by default

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalEmploymentTypes = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private employmentTypeService: EmploymentTypeService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadEmploymentTypes();
  }

  loadEmploymentTypes(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.employmentTypeService
      .getAllEmploymentTypes(this.includeInactive)
      .subscribe({
        next: (employmentTypes) => {
          this.employmentTypes = employmentTypes;
          this.totalEmploymentTypes = employmentTypes.length;
          this.applyFilter();
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = `Failed to load employment types: ${error.message}`;
          this.isLoading = false;
        },
      });
  }

  toggleIncludeInactive(): void {
    // Reload employment types when the toggle changes
    this.loadEmploymentTypes();
  }

  applyFilter(): void {
    const filterValue = this.searchTerm.toLowerCase();

    this.filteredEmploymentTypes = this.employmentTypes.filter(
      (type) =>
        type.name.toLowerCase().includes(filterValue) ||
        (type.created_by_username &&
          type.created_by_username.toLowerCase().includes(filterValue))
    );

    this.totalEmploymentTypes = this.filteredEmploymentTypes.length;

    if (this.paginator) {
      this.paginator.firstPage();
    }

    this.updateDisplayedEmploymentTypes();
  }

  updateDisplayedEmploymentTypes(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedEmploymentTypes = this.filteredEmploymentTypes.slice(
      startIndex,
      endIndex
    );
  }

  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedEmploymentTypes();
  }

  toggleStatus(employmentType: EmploymentType): void {
    const newStatus = !employmentType.is_active;

    this.employmentTypeService
      .toggleEmploymentTypeStatus(employmentType.id!, newStatus)
      .subscribe({
        next: (updatedType) => {
          employmentType.is_active = updatedType.is_active;
          this.showSnackBar(
            `Employment Type ${employmentType.name} ${
              newStatus ? 'activated' : 'deactivated'
            } successfully`
          );
        },
        error: (error) => {
          this.showSnackBar(`Error: ${error.message}`, true);
          // Revert toggle in UI
          employmentType.is_active = !newStatus;
        },
      });
  }

  deleteEmploymentType(employmentType: EmploymentType): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the employment type "${employmentType.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.employmentTypeService
          .deleteEmploymentType(employmentType.id!)
          .subscribe({
            next: () => {
              this.employmentTypes = this.employmentTypes.filter(
                (c) => c.id !== employmentType.id
              );
              this.applyFilter();
              this.showSnackBar(
                `Employment Type ${employmentType.name} deleted successfully`
              );
            },
            error: (error) => {
              this.showSnackBar(`Error: ${error.message}`, true);
            },
          });
      }
    });
  }

  bulkDeleteSelected(): void {
    const selectedEmploymentTypes = this.selection.selected;

    if (selectedEmploymentTypes.length === 0) {
      this.showSnackBar('No employment types selected for deletion', true);
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${selectedEmploymentTypes.length} selected employment types?`,
        confirmText: 'Delete All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = selectedEmploymentTypes.map((type) => type.id!);

        this.employmentTypeService.bulkDeleteEmploymentTypes(ids).subscribe({
          next: () => {
            this.employmentTypes = this.employmentTypes.filter(
              (type) => !ids.includes(type.id!)
            );
            this.selection.clear();
            this.applyFilter();
            this.showSnackBar(
              `Successfully deleted ${ids.length} employment types`
            );
          },
          error: (error) => {
            this.showSnackBar(`Error: ${error.message}`, true);
          },
        });
      }
    });
  }

  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedEmploymentTypes.length;
    return numSelected === numRows && numRows > 0;
  }

  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedEmploymentTypes);
    }
  }

  refreshList(): void {
    this.loadEmploymentTypes();
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
