import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';
import {
  QualificationService,
  Qualification,
} from '../../../../core/services/masters/qualification.service';

@Component({
  selector: 'app-qualification-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatDividerModule,
    MatSnackBarModule,
    MatDialogModule,
  ],
  templateUrl: './qualification-detail.component.html',
  styleUrls: ['./qualification-detail.component.scss'],
})
export class QualificationDetailComponent implements OnInit {
  qualification: Qualification | null = null;
  isLoading = false;
  errorMessage = '';
  qualificationId: number | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private qualificationService: QualificationService
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.qualificationId = +id;
        this.loadQualification(this.qualificationId);
      } else {
        this.errorMessage = 'Qualification ID not provided';
        this.isLoading = false;
      }
    });
  }

  loadQualification(id: number): void {
    this.isLoading = true;
    this.qualificationService.getQualificationById(id).subscribe({
      next: (qualification) => {
        this.qualification = qualification;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load qualification: ${error.message}`;
        this.isLoading = false;
      },
    });
  }

  editQualification(): void {
    if (this.qualificationId) {
      this.router.navigate(['../edit', this.qualificationId], {
        relativeTo: this.route,
      });
    }
  }

  deleteQualification(): void {
    if (!this.qualification) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the qualification "${this.qualification.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && this.qualification) {
        this.qualificationService
          .deleteQualification(this.qualification.id!)
          .subscribe({
            next: () => {
              this.showSnackBar(
                `Qualification ${this.qualification?.name} deleted successfully`
              );
              this.router.navigate(['../../'], { relativeTo: this.route });
            },
            error: (error) => {
              this.showSnackBar(`Error: ${error.message}`, true);
            },
          });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
