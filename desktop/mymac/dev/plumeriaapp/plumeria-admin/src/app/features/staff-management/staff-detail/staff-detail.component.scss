@import '../../../../styles/variables';

.staff-detail-container {
  @include card-container;
  max-width: 1200px;
  margin: 0 auto;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    .header-left {
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
        color: $text-color;
      }
    }

    .header-actions {
      display: flex;
      gap: $spacing-sm;

      button {
        min-width: 100px;
        height: 36px;
        border-radius: 6px;
        font-weight: 500;
        text-transform: none;

        mat-icon {
          margin-right: $spacing-xs;
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }
  }

  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl;
    text-align: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: $spacing-md;
    }

    p {
      margin: $spacing-md 0;
      color: $text-color-secondary;
    }
  }

  .detail-content {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }

  // Profile Overview Card
  .profile-overview {
    @include card-styling;
    background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
    color: white;

    .profile-header {
      display: flex;
      gap: $spacing-lg;
      align-items: flex-start;

      .profile-picture {
        flex-shrink: 0;
        width: 120px;
        height: 120px;
        border-radius: 50%;
        overflow: hidden;
        border: 4px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .profile-info {
        flex: 1;
        min-width: 0;

        h2 {
          margin: 0 0 $spacing-xs 0;
          font-size: 28px;
          font-weight: 600;
          color: white;
        }

        .designation {
          margin: 0 0 $spacing-md 0;
          font-size: 16px;
          color: rgba(255, 255, 255, 0.9);
          font-weight: 500;
        }

        .status-badges {
          margin-bottom: $spacing-md;

          mat-chip-set {
            mat-chip {
              &.status-active {
                background-color: rgba(76, 175, 80, 0.9);
                color: white;
              }

              &.status-inactive {
                background-color: rgba(244, 67, 54, 0.9);
                color: white;
              }

              &.employment-type {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
              }

              mat-icon {
                color: inherit;
              }
            }
          }
        }

        .quick-actions {
          display: flex;
          gap: $spacing-xs;

          button {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;

            &:hover {
              background-color: rgba(255, 255, 255, 0.3);
            }
          }
        }
      }

      .profile-stats {
        display: flex;
        flex-direction: column;
        gap: $spacing-md;
        min-width: 150px;

        .stat-item {
          text-align: center;
          padding: $spacing-sm;
          background-color: rgba(255, 255, 255, 0.1);
          border-radius: 8px;

          .stat-label {
            display: block;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
          }

          .stat-value {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: white;
          }
        }
      }
    }
  }

  // Detail Tabs Card
  .detail-tabs {
    @include card-styling;
    background-color: $card-background;

    mat-tab-group {
      .mat-mdc-tab-header {
        border-bottom: 1px solid $border-color;
      }

      .mat-mdc-tab-label {
        min-width: 120px;
        padding: 0 $spacing-md;
      }
    }

    .tab-content {
      padding: $spacing-lg 0;

      h3 {
        margin: $spacing-lg 0 $spacing-md 0;
        font-size: 18px;
        font-weight: 500;
        color: $text-color;
        border-bottom: 2px solid $primary-color;
        padding-bottom: $spacing-xs;

        &:first-child {
          margin-top: 0;
        }
      }

      mat-divider {
        margin: $spacing-lg 0;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: $spacing-lg;

      .info-item {
        display: flex;
        flex-direction: column;
        gap: $spacing-xs;

        &.full-width {
          grid-column: 1 / -1;
        }

        label {
          font-size: 14px;
          font-weight: 500;
          color: $text-color-secondary;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        span {
          font-size: 16px;
          color: $text-color;
          word-break: break-word;
          display: flex;
          align-items: center;
          gap: $spacing-xs;

          .link {
            color: $primary-color;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }

          .inline-action {
            margin-left: auto;
            width: 32px;
            height: 32px;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }

          &.status-text {
            font-weight: 500;
            padding: 4px 12px;
            border-radius: 16px;
            display: inline-block;
            text-align: center;
            min-width: 80px;

            &.active {
              background-color: #e8f5e8;
              color: #2e7d32;
            }

            &.inactive {
              background-color: #ffebee;
              color: #c62828;
            }
          }
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .detail-header {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-md;

      .header-left {
        justify-content: center;
      }

      .header-actions {
        justify-content: center;
        flex-wrap: wrap;
      }
    }

    .profile-overview {
      .profile-header {
        flex-direction: column;
        align-items: center;
        text-align: center;

        .profile-picture {
          width: 100px;
          height: 100px;
        }

        .profile-info {
          h2 {
            font-size: 24px;
          }
        }

        .profile-stats {
          flex-direction: row;
          justify-content: center;
          width: 100%;

          .stat-item {
            flex: 1;
            max-width: 120px;
          }
        }
      }
    }

    .detail-tabs {
      .info-grid {
        grid-template-columns: 1fr;
        gap: $spacing-md;
      }

      mat-tab-group {
        .mat-mdc-tab-label {
          min-width: 80px;
          padding: 0 $spacing-sm;
          font-size: 14px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    padding: $spacing-sm;

    .detail-header {
      .header-left h1 {
        font-size: 20px;
      }

      .header-actions button {
        min-width: 80px;
        font-size: 12px;

        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
        }
      }
    }

    .profile-overview {
      .profile-header {
        .profile-info h2 {
          font-size: 20px;
        }

        .profile-stats {
          .stat-item {
            padding: $spacing-xs;

            .stat-value {
              font-size: 14px;
            }
          }
        }
      }
    }

    .detail-tabs {
      .tab-content h3 {
        font-size: 16px;
      }

      .info-grid {
        .info-item {
          label {
            font-size: 12px;
          }

          span {
            font-size: 14px;
          }
        }
      }
    }
  }

  // Dark theme support
  @media (prefers-color-scheme: dark) {
    .detail-tabs {
      background-color: #2d2d2d;

      .tab-content h3 {
        color: #ffffff;
      }

      .info-grid {
        .info-item {
          label {
            color: #b0b0b0;
          }

          span {
            color: #ffffff;

            &.status-text {
              &.active {
                background-color: #1b5e20;
                color: #a5d6a7;
              }

              &.inactive {
                background-color: #b71c1c;
                color: #ffcdd2;
              }
            }
          }
        }
      }
    }
  }

  // Print styles
  @media print {
    .detail-header .header-actions,
    .quick-actions,
    .inline-action {
      display: none !important;
    }

    .profile-overview {
      background: white !important;
      color: black !important;
      box-shadow: none !important;
    }

    .detail-tabs {
      box-shadow: none !important;
    }
  }
}
