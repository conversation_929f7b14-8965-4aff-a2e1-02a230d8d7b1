<div class="staff-form-container">
  <div class="form-header">
    <h1>{{ pageTitle }}</h1>
    <div class="header-actions">
      <button mat-stroked-button color="accent" (click)="fillSampleData()"
              [disabled]="isSaving" *ngIf="!isEditMode"
              matTooltip="Fill form with sample data for testing">
        <mat-icon>auto_fix_high</mat-icon> Fill Sample Data
      </button>
      <button mat-stroked-button (click)="onCancel()" [disabled]="isSaving">
        <mat-icon>arrow_back</mat-icon> Back to List
      </button>
    </div>
  </div>

  <!-- Loading spinner for edit mode -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Loading staff data...</p>
  </div>

  <!-- Form content -->
  <form [formGroup]="staffForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
    <mat-card>
      <mat-card-content>
        <mat-tab-group>
          <!-- Personal Information Tab -->
          <mat-tab label="Personal Information">
            <div class="tab-content">
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Full Name *</mat-label>
                  <input matInput formControlName="staff_name" placeholder="Enter full name">
                  <mat-error>{{ getFieldError('staff_name') }}</mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Email Address *</mat-label>
                  <input matInput type="email" formControlName="staff_email" placeholder="Enter email address">
                  <mat-error>{{ getFieldError('staff_email') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Mobile Number *</mat-label>
                  <input matInput formControlName="staff_mobile" placeholder="Enter 10-digit mobile number">
                  <mat-error>{{ getFieldError('staff_mobile') }}</mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Gender *</mat-label>
                  <mat-select formControlName="gender">
                    <mat-option *ngFor="let gender of genderOptions" [value]="gender">
                      {{ gender }}
                    </mat-option>
                  </mat-select>
                  <mat-error>{{ getFieldError('gender') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Date of Birth *</mat-label>
                  <input matInput [matDatepicker]="dobPicker" formControlName="date_of_birth">
                  <mat-datepicker-toggle matSuffix [for]="dobPicker"></mat-datepicker-toggle>
                  <mat-datepicker #dobPicker></mat-datepicker>
                  <mat-error>{{ getFieldError('date_of_birth') }}</mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Marital Status *</mat-label>
                  <mat-select formControlName="marital_status">
                    <mat-option *ngFor="let status of maritalStatusOptions" [value]="status">
                      {{ status }}
                    </mat-option>
                  </mat-select>
                  <mat-error>{{ getFieldError('marital_status') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Blood Group</mat-label>
                  <mat-select formControlName="blood_group_id">
                    <mat-option value="">Select Blood Group</mat-option>
                    <mat-option *ngFor="let bloodGroup of bloodGroups" [value]="bloodGroup.id">
                      {{ bloodGroup.name }}
                    </mat-option>
                  </mat-select>
                  <mat-error>{{ getFieldError('blood_group_id') }}</mat-error>
                </mat-form-field>
              </div>
            </div>
          </mat-tab>

          <!-- Professional Information Tab -->
          <mat-tab label="Professional Details">
            <div class="tab-content">
              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Department *</mat-label>
                  <mat-select formControlName="department_id" placeholder="Select Department">
                    <!-- Search input inside dropdown -->
                    <mat-option disabled>
                      <mat-form-field appearance="outline" class="search-field">
                        <mat-label>Search departments...</mat-label>
                        <input matInput
                               [(ngModel)]="departmentSearchTerm"
                               (input)="filterDepartments()"
                               (click)="$event.stopPropagation()"
                               placeholder="Type to search...">
                        <mat-icon matSuffix>search</mat-icon>
                      </mat-form-field>
                    </mat-option>

                    <!-- Existing departments -->
                    <mat-option *ngFor="let dept of filteredDepartmentsList" [value]="dept.id">
                      {{ dept.name }}
                    </mat-option>

                    <!-- Create new option -->
                    <mat-option *ngIf="showCreateDepartment"
                                [value]="'create_new'"
                                class="create-new-option"
                                (click)="createNewDepartment()">
                      <mat-icon>add</mat-icon>
                      Create "{{ departmentSearchTerm }}"
                    </mat-option>

                    <!-- No results message -->
                    <mat-option *ngIf="filteredDepartmentsList.length === 0 && !showCreateDepartment && departmentSearchTerm"
                                disabled>
                      <em>No departments found</em>
                    </mat-option>
                  </mat-select>
                  <mat-error>{{ getFieldError('department_id') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Designation *</mat-label>
                  <mat-select formControlName="designation_id">
                    <mat-option value="">Select Designation</mat-option>
                    <mat-option *ngFor="let designation of designations" [value]="designation.id">
                      {{ designation.name }}
                    </mat-option>
                  </mat-select>
                  <mat-error>{{ getFieldError('designation_id') }}</mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Employment Type *</mat-label>
                  <mat-select formControlName="employment_type_id">
                    <mat-option value="">Select Employment Type</mat-option>
                    <mat-option *ngFor="let empType of employmentTypes" [value]="empType.id">
                      {{ empType.name }}
                    </mat-option>
                  </mat-select>
                  <mat-error>{{ getFieldError('employment_type_id') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>User Role</mat-label>
                  <mat-select formControlName="user_role_id">
                    <mat-option value="">Select User Role</mat-option>
                    <mat-option *ngFor="let role of roleOptions" [value]="role.id">
                      {{ role.name }}
                    </mat-option>
                  </mat-select>
                  <mat-error>{{ getFieldError('user_role_id') }}</mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Joining Date *</mat-label>
                  <input matInput [matDatepicker]="joiningPicker" formControlName="joining_date">
                  <mat-datepicker-toggle matSuffix [for]="joiningPicker"></mat-datepicker-toggle>
                  <mat-datepicker #joiningPicker></mat-datepicker>
                  <mat-error>{{ getFieldError('joining_date') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Qualification</mat-label>
                  <mat-select formControlName="qualification_id">
                    <mat-option value="">Select Qualification</mat-option>
                    <mat-option *ngFor="let qualification of qualifications" [value]="qualification.id">
                      {{ qualification.name }}
                    </mat-option>
                  </mat-select>
                  <mat-error>{{ getFieldError('qualification_id') }}</mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Salary Amount</mat-label>
                  <input matInput type="number" formControlName="salary_amount" placeholder="Enter salary amount">
                  <span matPrefix>₹ </span>
                  <mat-error>{{ getFieldError('salary_amount') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Last Salary Hike Date</mat-label>
                  <input matInput [matDatepicker]="hikePicker" formControlName="salary_last_hiked_date">
                  <mat-datepicker-toggle matSuffix [for]="hikePicker"></mat-datepicker-toggle>
                  <mat-datepicker #hikePicker></mat-datepicker>
                  <mat-error>{{ getFieldError('salary_last_hiked_date') }}</mat-error>
                </mat-form-field>
              </div>
            </div>
          </mat-tab>

          <!-- Emergency Contact Tab -->
          <mat-tab label="Emergency Contact">
            <div class="tab-content">
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Emergency Contact Name *</mat-label>
                  <input matInput formControlName="emergency_contact_name" placeholder="Enter emergency contact name">
                  <mat-error>{{ getFieldError('emergency_contact_name') }}</mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Emergency Contact Phone *</mat-label>
                  <input matInput formControlName="emergency_contact_phone" placeholder="Enter 10-digit phone number">
                  <mat-error>{{ getFieldError('emergency_contact_phone') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Relationship *</mat-label>
                  <input matInput formControlName="emergency_contact_relation" placeholder="e.g., Spouse, Parent, Sibling">
                  <mat-error>{{ getFieldError('emergency_contact_relation') }}</mat-error>
                </mat-form-field>
              </div>
            </div>
          </mat-tab>

          <!-- Address Information Tab -->
          <mat-tab label="Address & Location">
            <div class="tab-content">
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Address *</mat-label>
                  <textarea matInput formControlName="staff_address" rows="3"
                           placeholder="Enter complete address"></textarea>
                  <mat-error>{{ getFieldError('staff_address') }}</mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>State *</mat-label>
                  <mat-select formControlName="state_id">
                    <mat-option value="">Select State</mat-option>
                    <mat-option *ngFor="let state of states" [value]="state.id">
                      {{ state.name }}
                    </mat-option>
                  </mat-select>
                  <mat-error>{{ getFieldError('state_id') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>City *</mat-label>
                  <mat-select formControlName="city_id">
                    <mat-option value="">Select City</mat-option>
                    <mat-option *ngFor="let city of cities" [value]="city.id">
                      {{ city.name }}
                    </mat-option>
                  </mat-select>
                  <mat-error>{{ getFieldError('city_id') }}</mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Locality *</mat-label>
                  <mat-select formControlName="locality_id">
                    <mat-option value="">Select Locality</mat-option>
                    <mat-option *ngFor="let locality of localities" [value]="locality.id">
                      {{ locality.name }}
                    </mat-option>
                  </mat-select>
                  <mat-error>{{ getFieldError('locality_id') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Pincode *</mat-label>
                  <input matInput formControlName="pincode" placeholder="Enter 6-digit pincode">
                  <mat-error>{{ getFieldError('pincode') }}</mat-error>
                </mat-form-field>
              </div>
            </div>
          </mat-tab>

          <!-- Documents & Bank Details Tab -->
          <mat-tab label="Documents & Banking">
            <div class="tab-content">
              <h3>Identity Documents</h3>
              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Aadhaar Number</mat-label>
                  <input matInput formControlName="aadhaar_number" placeholder="Enter 12-digit Aadhaar number">
                  <mat-error>{{ getFieldError('aadhaar_number') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>PAN Number</mat-label>
                  <input matInput formControlName="pan_number" placeholder="Enter PAN number (**********)">
                  <mat-error>{{ getFieldError('pan_number') }}</mat-error>
                </mat-form-field>
              </div>

              <mat-divider></mat-divider>

              <h3>Bank Details</h3>
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Bank Name</mat-label>
                  <input matInput formControlName="bank_name" placeholder="Enter bank name">
                  <mat-error>{{ getFieldError('bank_name') }}</mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Account Number</mat-label>
                  <input matInput formControlName="account_number" placeholder="Enter account number">
                  <mat-error>{{ getFieldError('account_number') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>IFSC Code</mat-label>
                  <input matInput formControlName="ifsc_code" placeholder="Enter IFSC code">
                  <mat-error>{{ getFieldError('ifsc_code') }}</mat-error>
                </mat-form-field>
              </div>

              <mat-divider></mat-divider>

              <h3>Health Insurance</h3>
              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Insurance Provider</mat-label>
                  <input matInput formControlName="health_insurance_provider" placeholder="Enter insurance provider">
                  <mat-error>{{ getFieldError('health_insurance_provider') }}</mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Policy Number</mat-label>
                  <input matInput formControlName="health_insurance_number" placeholder="Enter policy number">
                  <mat-error>{{ getFieldError('health_insurance_number') }}</mat-error>
                </mat-form-field>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>

        <!-- Form Actions -->
        <div class="form-actions">
          <button mat-stroked-button type="button" (click)="onCancel()" [disabled]="isSaving">
            <mat-icon>cancel</mat-icon> Cancel
          </button>

          <button mat-raised-button color="primary" type="submit" [disabled]="isSaving || staffForm.invalid">
            <mat-spinner diameter="20" *ngIf="isSaving"></mat-spinner>
            <mat-icon *ngIf="!isSaving">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
            {{ isSaving ? 'Saving...' : (isEditMode ? 'Update Staff' : 'Create Staff') }}
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </form>
</div>
