.staff-form-container {
  padding: 24px;
  max-width: 900px;
  margin: 0 auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;

  h1 {
    font-size: 24px;
    font-weight: 500;
    color: #333;
    margin: 0;
  }
}

.header-actions {
  display: flex;
  gap: 12px;

  button {
    min-width: 120px;
    height: 40px;
    border-radius: 6px;
    font-weight: 500;
    text-transform: none;

    mat-icon {
      margin-right: 8px;
      font-size: 18px;
    }

    &[color="accent"] {
      background-color: #ff4081;
      color: white;
      border: 1px solid #ff4081;

      &:hover {
        background-color: #e91e63;
        border-color: #e91e63;
      }

      &:disabled {
        background-color: #f5f5f5;
        color: #bdbdbd;
        border-color: #e0e0e0;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  mat-spinner {
    margin-bottom: 16px;
  }

  p {
    color: #666;
    font-size: 16px;
    margin: 0;
  }
}

.form-card {
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

mat-card-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;

  mat-card-title {
    font-size: 24px;
    font-weight: 500;
    color: #333;
  }
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  text-align: center;
}

.error-message {
  color: #f44336;
  margin-bottom: 16px;
  font-size: 16px;
}

// Form layout with 3 fields per row
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: flex-start;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-field {
  flex: 1;
  min-width: 0; // Prevents flex items from overflowing

  &.full-width {
    flex: 1 1 100%;
  }

  &.half-width {
    flex: 1 1 calc(50% - 10px);
  }

  &.third-width {
    flex: 1 1 calc(33.333% - 14px);
  }
}

mat-form-field {
  width: 100%;

  .mat-mdc-form-field-label {
    font-weight: 500;
    color: #555;
  }

  &.mat-focused .mat-mdc-form-field-label {
    color: #3f51b5;
  }
}

// Special styling for specific field types
.photo-upload-field {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  text-align: center;

  &:hover {
    border-color: #3f51b5;
  }
}

.toggle-field {
  margin: 16px 0;

  mat-slide-toggle {
    .mat-mdc-slide-toggle-label {
      font-weight: 500;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;

  button {
    min-width: 120px;
    height: 44px;
    border-radius: 6px;
    font-weight: 500;
    text-transform: none;

    &[mat-raised-button] {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }

    mat-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }
}

// Tab styling for organized sections
mat-tab-group {
  margin-bottom: 24px;

  .mat-mdc-tab-header {
    border-bottom: 1px solid #e0e0e0;
  }

  .mat-mdc-tab-label {
    min-width: 120px;
    padding: 0 16px;
    font-weight: 500;

    &.mat-mdc-tab-label-active {
      color: #3f51b5;
    }
  }
}

.tab-content {
  padding: 24px 0;

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
    border-bottom: 2px solid #3f51b5;
    padding-bottom: 8px;
    display: inline-block;
  }
}

// Responsive design
@media (max-width: 1024px) {
  .staff-form-container {
    padding: 16px;
  }

  .form-row {
    gap: 16px;
  }

  .form-field {
    &.third-width {
      flex: 1 1 calc(50% - 8px);
    }
  }
}

@media (max-width: 768px) {
  .staff-form-container {
    padding: 12px;
  }

  .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .form-field {
    &.third-width,
    &.half-width {
      flex: 1 1 100%;
    }
  }

  .form-actions {
    flex-direction: column-reverse;

    button {
      width: 100%;
    }
  }

  mat-tab-group {
    .mat-mdc-tab-label {
      min-width: 80px;
      padding: 0 8px;
      font-size: 14px;
    }
  }
}

@media (max-width: 480px) {
  mat-card-header {
    mat-card-title {
      font-size: 20px;
    }
  }

  .tab-content h3 {
    font-size: 16px;
  }
}