@import '../../../../styles/variables';

.staff-form-container {
  @include card-container;
  max-width: 1200px;
  margin: 0 auto;

  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: $text-color;
    }

    .header-actions {
      display: flex;
      gap: $spacing-sm;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl;
    text-align: center;

    mat-spinner {
      margin-bottom: $spacing-md;
    }

    p {
      margin: 0;
      color: $text-color-secondary;
    }
  }

  mat-card {
    @include card-styling;
    background-color: $card-background;
  }

  // Tab styling
  mat-tab-group {
    .mat-mdc-tab-header {
      border-bottom: 1px solid $border-color;
    }

    .mat-mdc-tab-label {
      min-width: 120px;
      padding: 0 $spacing-md;
    }
  }

  .tab-content {
    padding: $spacing-lg 0;
    min-height: 400px;

    h3 {
      margin: $spacing-lg 0 $spacing-md 0;
      font-size: 18px;
      font-weight: 500;
      color: $text-color;
      border-bottom: 2px solid $primary-color;
      padding-bottom: $spacing-xs;
    }

    mat-divider {
      margin: $spacing-lg 0;
    }
  }

  // Form layout
  .form-row {
    display: flex;
    gap: $spacing-md;
    margin-bottom: $spacing-md;
    align-items: flex-start;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .full-width {
    flex: 1;
    width: 100%;
  }

  .half-width {
    flex: 1;
    min-width: 0;
  }

  .third-width {
    flex: 1;
    min-width: 0;
    max-width: 33.333%;
  }

  // Form field styling
  mat-form-field {
    &.mat-form-field-appearance-outline {
      .mat-mdc-form-field-outline {
        border-radius: 8px;
      }

      .mat-mdc-form-field-label {
        color: $text-color-secondary;
      }

      &.mat-focused {
        .mat-mdc-form-field-label {
          color: $primary-color;
        }
      }

      &.mat-form-field-invalid {
        .mat-mdc-form-field-outline-thick {
          border-color: $error-color;
        }
      }
    }

    // Input styling
    input,
    textarea {
      color: $text-color;
      
      &::placeholder {
        color: $text-color-secondary;
        opacity: 0.7;
      }
    }

    // Select styling
    mat-select {
      .mat-mdc-select-value {
        color: $text-color;
      }

      .mat-mdc-select-placeholder {
        color: $text-color-secondary;
      }
    }

    // Error styling
    mat-error {
      font-size: 12px;
      color: $error-color;
      margin-top: 4px;
    }

    // Prefix styling
    .mat-mdc-form-field-prefix {
      color: $text-color-secondary;
      font-weight: 500;
    }
  }

  // Date picker styling
  mat-datepicker-toggle {
    .mat-mdc-icon-button {
      color: $text-color-secondary;

      &:hover {
        color: $primary-color;
      }
    }
  }

  // Form actions
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-md;
    margin-top: $spacing-xl;
    padding-top: $spacing-lg;
    border-top: 1px solid $border-color;

    button {
      min-width: 120px;
      height: 44px;
      border-radius: 8px;
      font-weight: 500;
      text-transform: none;

      &[mat-stroked-button] {
        border-color: $border-color;
        color: $text-color-secondary;

        &:hover {
          background-color: $background-color;
          border-color: $text-color-secondary;
        }
      }

      &[mat-raised-button] {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:hover {
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        &:disabled {
          background-color: $disabled-color;
          color: $disabled-text-color;
          box-shadow: none;
        }
      }

      mat-spinner {
        margin-right: $spacing-xs;
      }

      mat-icon {
        margin-right: $spacing-xs;
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .form-header {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-md;

      .header-actions {
        justify-content: center;
      }
    }

    .form-row {
      flex-direction: column;
      gap: $spacing-sm;
    }

    .half-width,
    .third-width {
      width: 100%;
      max-width: none;
    }

    .tab-content {
      padding: $spacing-md 0;
      min-height: auto;
    }

    mat-tab-group {
      .mat-mdc-tab-label {
        min-width: 80px;
        padding: 0 $spacing-sm;
        font-size: 14px;
      }
    }

    .form-actions {
      flex-direction: column-reverse;
      gap: $spacing-sm;

      button {
        width: 100%;
      }
    }
  }

  @media (max-width: 480px) {
    padding: $spacing-sm;

    .form-header h1 {
      font-size: 20px;
    }

    .tab-content h3 {
      font-size: 16px;
    }

    mat-form-field {
      font-size: 14px;
    }
  }

  // Dark theme support
  @media (prefers-color-scheme: dark) {
    mat-card {
      background-color: #2d2d2d;
    }

    .tab-content h3 {
      color: #ffffff;
    }

    mat-form-field {
      input,
      textarea {
        color: #ffffff;
      }

      .mat-mdc-form-field-label {
        color: #b0b0b0;
      }

      &.mat-focused .mat-mdc-form-field-label {
        color: $primary-color;
      }
    }

    .form-actions {
      border-top-color: #404040;
    }
  }

  // Animation for form submission
  &.saving {
    pointer-events: none;
    opacity: 0.8;
  }

  // Required field indicator
  mat-form-field {
    .mat-mdc-form-field-label {
      &::after {
        content: ' *';
        color: $error-color;
      }
    }

    &.mat-form-field-required:not(.mat-form-field-invalid) {
      .mat-mdc-form-field-label::after {
        content: ' *';
      }
    }
  }

  // Success state
  &.form-success {
    .form-actions button[mat-raised-button] {
      background-color: $success-color;
      border-color: $success-color;
    }
  }
}
