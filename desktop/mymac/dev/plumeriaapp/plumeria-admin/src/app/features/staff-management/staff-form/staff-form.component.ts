import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { FormControl } from '@angular/forms';
import { Observable, of } from 'rxjs';
import {
  map,
  startWith,
  debounceTime,
  distinctUntilChanged,
} from 'rxjs/operators';

import { StaffService } from '../../../core/services/staff.service';
import { DepartmentService } from '../../../core/services/masters/department.service';
import { DesignationService } from '../../../core/services/masters/designation.service';
import { EmploymentTypeService } from '../../../core/services/masters/employment-type.service';
import { BloodGroupService } from '../../../core/services/masters/blood-group.service';
import { QualificationService } from '../../../core/services/masters/qualification.service';
import { LocalityService } from '../../../core/services/masters/locality.service';
import { CityService } from '../../../core/services/masters/city.service';
import { StateService } from '../../../core/services/masters/state.service';
import { RoleService } from '../../../core/services/role.service';

import {
  Staff,
  StaffCreateRequest,
  StaffUpdateRequest,
} from '../../../core/models/staff';
import { Department } from '../../../core/models/masters/department';
import { Designation } from '../../../core/models/masters/designation';
import { EmploymentType } from '../../../core/models/masters/employment-type';
import { BloodGroup } from '../../../core/models/masters/blood-group';
import { Qualification } from '../../../core/models/masters/qualification';
import { Locality } from '../../../core/models/masters/locality';
import { City } from '../../../core/models/masters/city';
import { State } from '../../../core/models/masters/state';
import { Role } from '../../../core/models/user';

// Local interface to ensure proper typing
interface RoleOption {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
}

@Component({
  selector: 'app-staff-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTabsModule,
    MatDividerModule,
    MatAutocompleteModule,
  ],
  templateUrl: './staff-form.component.html',
  styleUrls: ['./staff-form.component.scss'],
})
export class StaffFormComponent implements OnInit {
  staffForm!: FormGroup;
  isEditMode = false;
  isLoading = false;
  isSaving = false;
  staffId: number | null = null;
  currentStaff: Staff | null = null;

  // Master data
  departments: Department[] = [];
  designations: Designation[] = [];
  employmentTypes: EmploymentType[] = [];
  bloodGroups: BloodGroup[] = [];
  qualifications: Qualification[] = [];
  localities: Locality[] = [];
  cities: City[] = [];
  states: State[] = [];
  roles: RoleOption[] = [];

  // Loading states for dropdowns
  loadingDepartments = false;
  loadingDesignations = false;
  loadingEmploymentTypes = false;
  loadingBloodGroups = false;
  loadingQualifications = false;
  loadingLocalities = false;
  loadingCities = false;
  loadingStates = false;
  loadingRoles = false;

  // Gender and marital status options
  genderOptions = ['Male', 'Female', 'Other'];
  maritalStatusOptions = ['Single', 'Married', 'Divorced', 'Widowed'];

  // Searchable dropdown for Department
  departmentSearchTerm: string = '';
  filteredDepartmentsList: Department[] = [];
  showCreateDepartment: boolean = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private staffService: StaffService,
    private departmentService: DepartmentService,
    private designationService: DesignationService,
    private employmentTypeService: EmploymentTypeService,
    private bloodGroupService: BloodGroupService,
    private qualificationService: QualificationService,
    private localityService: LocalityService,
    private cityService: CityService,
    private stateService: StateService,
    private roleService: RoleService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadMasterData();
    this.checkEditMode();
    this.initializeDepartmentFilter();
  }

  private initializeForm(): void {
    this.staffForm = this.fb.group({
      // Personal Information
      staff_name: ['', [Validators.required, Validators.minLength(2)]],
      staff_email: ['', [Validators.required, Validators.email]],
      staff_mobile: [
        '',
        [Validators.required, Validators.pattern(/^[6-9]\d{9}$/)],
      ],
      gender: ['', Validators.required],
      date_of_birth: ['', Validators.required],
      marital_status: ['', Validators.required],
      blood_group_id: [''],

      // Professional Information
      user_role_id: [''],
      joining_date: ['', Validators.required],
      designation_id: ['', Validators.required],
      employment_type_id: ['', Validators.required],
      department_id: ['', Validators.required],
      salary_amount: ['', [Validators.min(0)]],
      salary_last_hiked_date: [''],

      // Emergency Contact
      emergency_contact_name: [
        '',
        [Validators.required, Validators.minLength(2)],
      ],
      emergency_contact_phone: [
        '',
        [Validators.required, Validators.pattern(/^[6-9]\d{9}$/)],
      ],
      emergency_contact_relation: [
        '',
        [Validators.required, Validators.minLength(2)],
      ],

      // Address Information
      staff_address: ['', [Validators.required, Validators.minLength(10)]],
      locality_id: ['', Validators.required],
      city_id: ['', Validators.required],
      state_id: ['', Validators.required],
      pincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],

      // Identity Documents
      aadhaar_number: ['', [Validators.pattern(/^\d{12}$/)]],
      pan_number: ['', [Validators.pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/)]],
      qualification_id: [''],

      // Bank Details
      bank_name: [''],
      account_number: [''],
      ifsc_code: ['', [Validators.pattern(/^[A-Z]{4}0[A-Z0-9]{6}$/)]],

      // Health Insurance
      health_insurance_provider: [''],
      health_insurance_number: [''],
    });
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id && id !== 'new') {
      this.isEditMode = true;
      this.staffId = parseInt(id, 10);
      this.loadStaffData();
    }
  }

  private loadStaffData(): void {
    if (!this.staffId) return;

    this.isLoading = true;
    this.staffService.getStaffById(this.staffId).subscribe({
      next: (response) => {
        if (response.success) {
          this.currentStaff = response.data;
          this.populateForm(response.data);
        } else {
          this.snackBar.open(
            response.message || 'Failed to load staff data',
            'Close',
            {
              duration: 3000,
            }
          );
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open(
          'Error loading staff data: ' + this.getErrorMessage(error),
          'Close',
          {
            duration: 3000,
          }
        );
        this.isLoading = false;
      },
    });
  }

  private populateForm(staff: Staff): void {
    this.staffForm.patchValue({
      staff_name: staff.staff_name,
      staff_email: staff.staff_email,
      staff_mobile: staff.staff_mobile,
      gender: staff.gender,
      date_of_birth: new Date(staff.date_of_birth),
      marital_status: staff.marital_status,
      blood_group_id: staff.blood_group_id,
      user_role_id: staff.user_role_id,
      joining_date: new Date(staff.joining_date),
      designation_id: staff.designation_id,
      employment_type_id: staff.employment_type_id,
      department_id: staff.department_id,
      salary_amount: staff.salary_amount,
      salary_last_hiked_date: staff.salary_last_hiked_date
        ? new Date(staff.salary_last_hiked_date)
        : null,
      emergency_contact_name: staff.emergency_contact_name,
      emergency_contact_phone: staff.emergency_contact_phone,
      emergency_contact_relation: staff.emergency_contact_relation,
      staff_address: staff.staff_address,
      locality_id: staff.locality_id,
      city_id: staff.city_id,
      state_id: staff.state_id,
      pincode: staff.pincode,
      aadhaar_number: staff.aadhaar_number,
      pan_number: staff.pan_number,
      qualification_id: staff.qualification_id,
      bank_name: staff.bank_name,
      account_number: staff.account_number,
      ifsc_code: staff.ifsc_code,
      health_insurance_provider: staff.health_insurance_provider,
      health_insurance_number: staff.health_insurance_number,
    });
  }

  private loadMasterData(): void {
    this.loadDepartments();
    this.loadDesignations();
    this.loadEmploymentTypes();
    this.loadBloodGroups();
    this.loadQualifications();
    this.loadLocalities();
    this.loadCities();
    this.loadStates();
    this.loadRoles();
  }

  private loadDepartments(): void {
    this.loadingDepartments = true;
    this.departmentService.getDepartments(false).subscribe({
      next: (response) => {
        if (response.success) {
          this.departments = Array.isArray(response.data) ? response.data : [];
          // Initialize the filtered list after departments are loaded
          this.filteredDepartmentsList = [...this.departments];
        }
        this.loadingDepartments = false;
      },
      error: () => {
        this.loadingDepartments = false;
      },
    });
  }

  private loadDesignations(): void {
    this.loadingDesignations = true;
    this.designationService.getDesignations(false).subscribe({
      next: (response) => {
        if (response.success) {
          this.designations = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingDesignations = false;
      },
      error: () => {
        this.loadingDesignations = false;
      },
    });
  }

  private loadEmploymentTypes(): void {
    this.loadingEmploymentTypes = true;
    this.employmentTypeService.getAllEmploymentTypes(false).subscribe({
      next: (data) => {
        this.employmentTypes = Array.isArray(data) ? data : [];
        this.loadingEmploymentTypes = false;
      },
      error: () => {
        this.loadingEmploymentTypes = false;
      },
    });
  }

  private loadBloodGroups(): void {
    this.loadingBloodGroups = true;
    this.bloodGroupService.getBloodGroups(false).subscribe({
      next: (response) => {
        if (response.success) {
          this.bloodGroups = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingBloodGroups = false;
      },
      error: () => {
        this.loadingBloodGroups = false;
      },
    });
  }

  private loadQualifications(): void {
    this.loadingQualifications = true;
    this.qualificationService.getAllQualifications(false).subscribe({
      next: (data) => {
        this.qualifications = Array.isArray(data) ? data : [];
        this.loadingQualifications = false;
      },
      error: () => {
        this.loadingQualifications = false;
      },
    });
  }

  private loadLocalities(): void {
    this.loadingLocalities = true;
    this.localityService.getLocalities(false).subscribe({
      next: (response) => {
        if (response.success) {
          this.localities = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingLocalities = false;
      },
      error: () => {
        this.loadingLocalities = false;
      },
    });
  }

  private loadCities(): void {
    this.loadingCities = true;
    this.cityService.getCities(false).subscribe({
      next: (response) => {
        if (response.success) {
          this.cities = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingCities = false;
      },
      error: () => {
        this.loadingCities = false;
      },
    });
  }

  private loadStates(): void {
    this.loadingStates = true;
    this.stateService.getStates(false).subscribe({
      next: (response) => {
        if (response.success) {
          this.states = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingStates = false;
      },
      error: () => {
        this.loadingStates = false;
      },
    });
  }

  private loadRoles(): void {
    this.loadingRoles = true;
    this.roleService.getRoles().subscribe({
      next: (response) => {
        if (response.success) {
          this.roles = Array.isArray(response.data)
            ? (response.data as RoleOption[])
            : [];
        }
        this.loadingRoles = false;
      },
      error: () => {
        this.loadingRoles = false;
      },
    });
  }

  // Department Searchable Dropdown Setup
  private initializeDepartmentFilter(): void {
    this.filteredDepartmentsList = [...this.departments];
  }

  filterDepartments(): void {
    const searchTerm = this.departmentSearchTerm.toLowerCase().trim();

    if (!searchTerm) {
      this.filteredDepartmentsList = [...this.departments];
      this.showCreateDepartment = false;
      return;
    }

    // Filter existing departments
    this.filteredDepartmentsList = this.departments.filter((dept) =>
      dept.name.toLowerCase().includes(searchTerm)
    );

    // Show create option if no exact match found and search term is valid
    const exactMatch = this.departments.some(
      (dept) => dept.name.toLowerCase() === searchTerm
    );

    this.showCreateDepartment = !exactMatch && searchTerm.length >= 2;
  }

  createNewDepartment(): void {
    const departmentName = this.departmentSearchTerm.trim();

    if (!departmentName || departmentName.length < 2) {
      this.snackBar.open('Please enter a valid department name', 'Close', {
        duration: 3000,
      });
      return;
    }
    this.loadingDepartments = true;

    // Generate a code from the department name
    const departmentCode = this.generateDepartmentCode(departmentName);

    const newDepartment = {
      name: departmentName,
      code: departmentCode,
      is_active: true,
    };

    this.departmentService.createDepartment(newDepartment).subscribe({
      next: (response) => {
        if (response.success) {
          const newDept = Array.isArray(response.data)
            ? response.data[0]
            : response.data;

          // Add the new department to the list
          this.departments.push(newDept);

          // Set it as selected
          this.staffForm.patchValue({
            department_id: newDept.id,
          });

          // Clear search and update filtered list
          this.departmentSearchTerm = '';
          this.filteredDepartmentsList = [...this.departments];
          this.showCreateDepartment = false;

          this.snackBar.open(
            `Department "${departmentName}" created successfully!`,
            'Close',
            {
              duration: 3000,
            }
          );
        } else {
          this.snackBar.open(
            response.message || 'Failed to create department',
            'Close',
            {
              duration: 3000,
            }
          );
        }
        this.loadingDepartments = false;
      },
      error: (error) => {
        this.snackBar.open(
          'Error creating department: ' + this.getErrorMessage(error),
          'Close',
          {
            duration: 3000,
          }
        );
        this.loadingDepartments = false;
      },
    });
  }

  private generateDepartmentCode(departmentName: string): string {
    // Generate a code from the department name
    // Remove special characters, convert to uppercase, and take first 3-6 characters
    let code = departmentName
      .replace(/[^a-zA-Z0-9\s]/g, '') // Remove special characters
      .replace(/\s+/g, '') // Remove spaces
      .toUpperCase();

    // If the name has multiple words, take first letter of each word
    const words = departmentName.trim().split(/\s+/);
    if (words.length > 1) {
      code = words.map((word) => word.charAt(0).toUpperCase()).join('');
    }

    // Ensure code is between 2-6 characters
    if (code.length < 2) {
      code = code.padEnd(2, '0');
    } else if (code.length > 6) {
      code = code.substring(0, 6);
    }

    // Add a random number to avoid conflicts
    const randomNum = Math.floor(Math.random() * 99) + 1;
    code = code + randomNum.toString().padStart(2, '0');

    return code;
  }

  fillSampleData(): void {
    // Generate random sample data for testing
    const sampleData = {
      // Personal Information
      staff_name: this.getRandomName(),
      staff_email: this.getRandomEmail(),
      staff_mobile: this.getRandomMobile(),
      gender: this.getRandomGender(),
      date_of_birth: this.getRandomDateOfBirth(),
      marital_status: this.getRandomMaritalStatus(),
      blood_group_id: this.getRandomBloodGroupId(),

      // Professional Information
      department_id: this.getRandomDepartmentId(),
      designation_id: this.getRandomDesignationId(),
      employment_type_id: this.getRandomEmploymentTypeId(),
      user_role_id: this.getRandomUserRoleId(),
      joining_date: this.getRandomJoiningDate(),
      qualification_id: this.getRandomQualificationId(),
      salary_amount: this.getRandomSalary(),
      salary_last_hiked_date: this.getRandomSalaryHikeDate(),

      // Emergency Contact
      emergency_contact_name: this.getRandomEmergencyContactName(),
      emergency_contact_phone: this.getRandomMobile(),
      emergency_contact_relation: this.getRandomRelation(),

      // Address Information
      staff_address: this.getRandomAddress(),
      state_id: this.getRandomStateId(),
      city_id: this.getRandomCityId(),
      locality_id: this.getRandomLocalityId(),
      pincode: this.getRandomPincode(),

      // Documents & Banking
      aadhaar_number: this.getRandomAadhaar(),
      pan_number: this.getRandomPAN(),
      bank_name: this.getRandomBankName(),
      account_number: this.getRandomAccountNumber(),
      ifsc_code: this.getRandomIFSC(),

      // Health Insurance
      health_insurance_provider: this.getRandomInsuranceProvider(),
      health_insurance_number: this.getRandomInsuranceNumber(),
    };

    // Patch the form with sample data
    this.staffForm.patchValue(sampleData);

    // Show success message
    this.snackBar.open('Sample data filled successfully!', 'Close', {
      duration: 2000,
    });
  }

  onSubmit(): void {
    if (this.staffForm.invalid) {
      this.markFormGroupTouched();
      this.snackBar.open(
        'Please fill in all required fields correctly',
        'Close',
        {
          duration: 3000,
        }
      );
      return;
    }

    this.isSaving = true;
    const formData = this.prepareFormData();

    if (this.isEditMode && this.staffId) {
      const updateData = {
        ...formData,
        id: this.staffId,
      } as StaffUpdateRequest;
      this.updateStaff(updateData);
    } else {
      const createData = formData as StaffCreateRequest;
      this.createStaff(createData);
    }
  }

  private prepareFormData(): StaffCreateRequest | StaffUpdateRequest {
    const formValue = this.staffForm.value;

    // Convert dates to ISO strings
    const data = {
      ...formValue,
      date_of_birth: this.formatDate(formValue.date_of_birth),
      joining_date: this.formatDate(formValue.joining_date),
      salary_last_hiked_date: formValue.salary_last_hiked_date
        ? this.formatDate(formValue.salary_last_hiked_date)
        : null,
    };

    // Remove empty optional fields
    Object.keys(data).forEach((key) => {
      if (data[key] === '' || data[key] === null || data[key] === undefined) {
        delete data[key];
      }
    });

    return data;
  }

  private formatDate(date: Date): string {
    if (!date) return '';
    return new Date(date).toISOString().split('T')[0];
  }

  private createStaff(data: StaffCreateRequest): void {
    this.staffService.createStaff(data).subscribe({
      next: (response) => {
        if (response.success) {
          this.snackBar.open('Staff member created successfully', 'Close', {
            duration: 3000,
          });
          this.router.navigate(['/admin/staff-management']);
        } else {
          this.snackBar.open(
            response.message || 'Failed to create staff member',
            'Close',
            {
              duration: 3000,
            }
          );
        }
        this.isSaving = false;
      },
      error: (error) => {
        this.snackBar.open(
          'Error creating staff member: ' + this.getErrorMessage(error),
          'Close',
          {
            duration: 3000,
          }
        );
        this.isSaving = false;
      },
    });
  }

  private updateStaff(data: StaffUpdateRequest): void {
    if (!this.staffId) return;

    this.staffService
      .updateStaff(this.staffId, { ...data, id: this.staffId })
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.snackBar.open('Staff member updated successfully', 'Close', {
              duration: 3000,
            });
            this.router.navigate(['/admin/staff-management']);
          } else {
            this.snackBar.open(
              response.message || 'Failed to update staff member',
              'Close',
              {
                duration: 3000,
              }
            );
          }
          this.isSaving = false;
        },
        error: (error) => {
          this.snackBar.open(
            'Error updating staff member: ' + this.getErrorMessage(error),
            'Close',
            {
              duration: 3000,
            }
          );
          this.isSaving = false;
        },
      });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.staffForm.controls).forEach((key) => {
      const control = this.staffForm.get(key);
      control?.markAsTouched();
    });
  }

  onCancel(): void {
    this.router.navigate(['/admin/staff-management']);
  }

  getFieldError(fieldName: string): string {
    const control = this.staffForm.get(fieldName);
    if (control?.errors && control.touched) {
      if (control.errors['required']) {
        return `${this.getFieldLabel(fieldName)} is required`;
      }
      if (control.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (control.errors['pattern']) {
        return this.getPatternError(fieldName);
      }
      if (control.errors['minlength']) {
        return `${this.getFieldLabel(fieldName)} must be at least ${
          control.errors['minlength'].requiredLength
        } characters`;
      }
      if (control.errors['min']) {
        return `${this.getFieldLabel(
          fieldName
        )} must be greater than or equal to ${control.errors['min'].min}`;
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      staff_name: 'Staff Name',
      staff_email: 'Email',
      staff_mobile: 'Mobile Number',
      gender: 'Gender',
      date_of_birth: 'Date of Birth',
      marital_status: 'Marital Status',
      joining_date: 'Joining Date',
      designation_id: 'Designation',
      employment_type_id: 'Employment Type',
      department_id: 'Department',
      emergency_contact_name: 'Emergency Contact Name',
      emergency_contact_phone: 'Emergency Contact Phone',
      emergency_contact_relation: 'Emergency Contact Relation',
      staff_address: 'Address',
      locality_id: 'Locality',
      city_id: 'City',
      state_id: 'State',
      pincode: 'Pincode',
      aadhaar_number: 'Aadhaar Number',
      pan_number: 'PAN Number',
      ifsc_code: 'IFSC Code',
      salary_amount: 'Salary Amount',
    };
    return labels[fieldName] || fieldName;
  }

  private getPatternError(fieldName: string): string {
    const patterns: { [key: string]: string } = {
      staff_mobile: 'Mobile number must be 10 digits starting with 6-9',
      emergency_contact_phone:
        'Phone number must be 10 digits starting with 6-9',
      pincode: 'Pincode must be 6 digits',
      aadhaar_number: 'Aadhaar number must be 12 digits',
      pan_number: 'PAN number format: **********',
      ifsc_code: 'IFSC code format: ABCD0123456',
    };
    return patterns[fieldName] || 'Invalid format';
  }

  private getErrorMessage(error: any): string {
    if (error.error?.message) {
      return error.error.message;
    }
    if (error.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  }

  get pageTitle(): string {
    return this.isEditMode ? 'Edit Staff Member' : 'Add New Staff Member';
  }

  // Getter to ensure proper typing for roles
  get roleOptions(): RoleOption[] {
    return this.roles;
  }

  // Sample data generation methods
  private getRandomName(): string {
    const firstNames = [
      'Rajesh',
      'Priya',
      'Amit',
      'Sunita',
      'Vikram',
      'Kavya',
      'Arjun',
      'Meera',
      'Rohit',
      'Anita',
      'Sanjay',
      'Pooja',
      'Kiran',
      'Deepika',
      'Manoj',
      'Shreya',
      'Anil',
      'Ritu',
      'Suresh',
      'Neha',
      'Rahul',
      'Divya',
      'Ajay',
      'Swati',
    ];
    const lastNames = [
      'Sharma',
      'Patel',
      'Singh',
      'Kumar',
      'Gupta',
      'Agarwal',
      'Jain',
      'Verma',
      'Yadav',
      'Reddy',
      'Nair',
      'Iyer',
      'Chopra',
      'Malhotra',
      'Bansal',
      'Joshi',
    ];

    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    return `${firstName} ${lastName}`;
  }

  private getRandomEmail(): string {
    const name = this.getRandomName().toLowerCase().replace(' ', '.');
    const domains = ['gmail.com', 'yahoo.com', 'outlook.com', 'company.com'];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    return `${name}@${domain}`;
  }

  private getRandomMobile(): string {
    const prefixes = ['6', '7', '8', '9'];
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const remaining = Math.floor(Math.random() * *********) + *********;
    return `${prefix}${remaining}`;
  }

  private getRandomGender(): string {
    const genders = ['Male', 'Female'];
    return genders[Math.floor(Math.random() * genders.length)];
  }

  private getRandomDateOfBirth(): Date {
    const start = new Date(1970, 0, 1);
    const end = new Date(2000, 11, 31);
    return new Date(
      start.getTime() + Math.random() * (end.getTime() - start.getTime())
    );
  }

  private getRandomMaritalStatus(): string {
    const statuses = ['Single', 'Married', 'Divorced', 'Widowed'];
    return statuses[Math.floor(Math.random() * statuses.length)];
  }

  private getRandomBloodGroupId(): number | null {
    if (this.bloodGroups.length === 0) return null;
    return this.bloodGroups[Math.floor(Math.random() * this.bloodGroups.length)]
      .id;
  }

  private getRandomDepartmentId(): number | null {
    if (this.departments.length === 0) return null;
    return this.departments[Math.floor(Math.random() * this.departments.length)]
      .id;
  }

  private getRandomDesignationId(): number | null {
    if (this.designations.length === 0) return null;
    return this.designations[
      Math.floor(Math.random() * this.designations.length)
    ].id;
  }

  private getRandomEmploymentTypeId(): number | null {
    if (this.employmentTypes.length === 0) return null;
    return this.employmentTypes[
      Math.floor(Math.random() * this.employmentTypes.length)
    ].id;
  }

  private getRandomUserRoleId(): number | null {
    if (this.roles.length === 0) return null;
    return this.roles[Math.floor(Math.random() * this.roles.length)].id;
  }

  private getRandomJoiningDate(): Date {
    const start = new Date(2020, 0, 1);
    const end = new Date();
    return new Date(
      start.getTime() + Math.random() * (end.getTime() - start.getTime())
    );
  }

  private getRandomQualificationId(): number | null {
    if (this.qualifications.length === 0) return null;
    return this.qualifications[
      Math.floor(Math.random() * this.qualifications.length)
    ].id;
  }

  private getRandomSalary(): number {
    return Math.floor(Math.random() * 500000) + 200000; // 2L to 7L
  }

  private getRandomSalaryHikeDate(): Date | null {
    if (Math.random() < 0.5) return null; // 50% chance of no hike date
    const start = new Date(2022, 0, 1);
    const end = new Date();
    return new Date(
      start.getTime() + Math.random() * (end.getTime() - start.getTime())
    );
  }

  private getRandomEmergencyContactName(): string {
    return this.getRandomName();
  }

  private getRandomRelation(): string {
    const relations = [
      'Father',
      'Mother',
      'Spouse',
      'Brother',
      'Sister',
      'Friend',
    ];
    return relations[Math.floor(Math.random() * relations.length)];
  }

  private getRandomAddress(): string {
    const addresses = [
      '123 MG Road, Near City Mall',
      '456 Brigade Road, Opposite Metro Station',
      '789 Commercial Street, Behind Bus Stand',
      '321 Residency Road, Near Hospital',
      '654 Infantry Road, Close to Park',
      '987 Church Street, Next to School',
    ];
    return addresses[Math.floor(Math.random() * addresses.length)];
  }

  private getRandomStateId(): number | null {
    if (this.states.length === 0) return null;
    return this.states[Math.floor(Math.random() * this.states.length)].id;
  }

  private getRandomCityId(): number | null {
    if (this.cities.length === 0) return null;
    return this.cities[Math.floor(Math.random() * this.cities.length)].id;
  }

  private getRandomLocalityId(): number | null {
    if (this.localities.length === 0) return null;
    return this.localities[Math.floor(Math.random() * this.localities.length)]
      .id;
  }

  private getRandomPincode(): string {
    return Math.floor(Math.random() * 900000 + 100000).toString();
  }

  private getRandomAadhaar(): string {
    return Math.floor(Math.random() * ************ + **********00).toString();
  }

  private getRandomPAN(): string {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '**********';
    let pan = '';

    // First 5 characters are letters
    for (let i = 0; i < 5; i++) {
      pan += letters.charAt(Math.floor(Math.random() * letters.length));
    }

    // Next 4 characters are numbers
    for (let i = 0; i < 4; i++) {
      pan += numbers.charAt(Math.floor(Math.random() * numbers.length));
    }

    // Last character is a letter
    pan += letters.charAt(Math.floor(Math.random() * letters.length));

    return pan;
  }

  private getRandomBankName(): string {
    const banks = [
      'State Bank of India',
      'HDFC Bank',
      'ICICI Bank',
      'Axis Bank',
      'Punjab National Bank',
      'Bank of Baroda',
      'Canara Bank',
      'Union Bank',
    ];
    return banks[Math.floor(Math.random() * banks.length)];
  }

  private getRandomAccountNumber(): string {
    return Math.floor(
      Math.random() * ************0000 + **********000000
    ).toString();
  }

  private getRandomIFSC(): string {
    const bankCodes = [
      'SBIN',
      'HDFC',
      'ICIC',
      'UTIB',
      'PUNB',
      'BARB',
      'CNRB',
      'UBIN',
    ];
    const bankCode = bankCodes[Math.floor(Math.random() * bankCodes.length)];
    const branchCode = Math.floor(Math.random() * 900000 + 100000);
    return `${bankCode}0${branchCode}`;
  }

  private getRandomInsuranceProvider(): string {
    const providers = [
      'Star Health Insurance',
      'HDFC ERGO',
      'ICICI Lombard',
      'Bajaj Allianz',
      'New India Assurance',
      'Oriental Insurance',
      'United India Insurance',
    ];
    return providers[Math.floor(Math.random() * providers.length)];
  }

  private getRandomInsuranceNumber(): string {
    return (
      'POL' + Math.floor(Math.random() * ********** + **********).toString()
    );
  }
}
