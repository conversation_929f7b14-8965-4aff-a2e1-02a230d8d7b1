@import '../../../../styles/variables';
@import '../../../../styles/mixins';

.staff-list-container {
  @include card-container;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: $text-color;
    }
  }

  mat-card {
    @include card-styling;
    background-color: $card-background;
  }

  .filter-container {
    margin-bottom: $spacing-lg;
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-md;
    align-items: flex-end;

    .search-field {
      flex: 2;
      min-width: 300px;

      mat-form-field {
        width: 100%;
      }
    }

    .filter-field {
      flex: 1;
      min-width: 200px;

      mat-form-field {
        width: 100%;
      }
    }

    .filter-actions {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
    }
  }

  .action-buttons {
    margin: $spacing-lg 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions {
      display: flex;
      align-items: center;
      gap: $spacing-md;

      .show-inactive-toggle {
        margin-left: $spacing-md;
      }
    }

    .right-actions {
      .total-count {
        color: $text-color-secondary;
        font-size: 14px;
      }
    }
  }

  .loading-container,
  .error-container,
  .no-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl;
    text-align: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: $spacing-md;
    }

    p {
      margin: $spacing-md 0;
      color: $text-color-secondary;
    }
  }

  .table-container {
    border-radius: 8px;
    overflow: hidden;

    table {
      width: 100%;

      th {
        background-color: $table-header-background;
        color: $table-header-text;
        font-weight: 600;
        padding: $spacing-md;
      }

      td {
        padding: $spacing-md;
        border-bottom: 1px solid $border-color;
      }

      tr:hover {
        background-color: $table-row-hover;
      }
    }

    // Profile picture styling
    .profile-picture {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: $background-color;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    // Staff name cell styling
    .staff-name-cell {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .name {
        font-weight: 500;
        color: $text-color;
      }

      .gender {
        font-size: 12px;
        color: $text-color-secondary;
        text-transform: uppercase;
      }
    }

    // Link styling
    .email-link,
    .phone-link {
      color: $primary-color;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    // Badge styling
    .department-badge,
    .designation-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      background-color: $accent-color;
      color: white;
    }

    .designation-badge {
      background-color: $secondary-color;
    }

    // Status badge styling
    .status-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;

      &.active {
        background-color: #e8f5e8;
        color: #2e7d32;
      }

      &.inactive {
        background-color: #ffebee;
        color: #c62828;
      }
    }

    // Action buttons
    .mat-mdc-icon-button {
      margin: 0 2px;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .filter-container {
      .search-field,
      .filter-field {
        flex: 1 1 100%;
        min-width: unset;
      }
    }

    .action-buttons {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-md;

      .left-actions {
        justify-content: center;
      }

      .right-actions {
        text-align: center;
      }
    }

    .table-container {
      overflow-x: auto;

      table {
        min-width: 800px;
      }
    }
  }

  // Dark theme support
  @media (prefers-color-scheme: dark) {
    .profile-picture {
      background-color: #424242;
    }

    .status-badge {
      &.active {
        background-color: #1b5e20;
        color: #a5d6a7;
      }

      &.inactive {
        background-color: #b71c1c;
        color: #ffcdd2;
      }
    }
  }
}
