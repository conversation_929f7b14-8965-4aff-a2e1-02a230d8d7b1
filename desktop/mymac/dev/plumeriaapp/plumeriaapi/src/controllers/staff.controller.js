const Staff = require('../models/staff.model');
const StaffService = require('../services/staff.service');

/**
 * Staff Controller
 * Handles HTTP requests for staff management
 * @class StaffController
 */
const StaffController = {
  /**
   * Get all staff with pagination and filtering
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllStaff: async (req, res) => {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        departmentId,
        designationId,
        includeInactive = false
      } = req.query;

      // Calculate pagination
      const offset = (parseInt(page) - 1) * parseInt(limit);

      const options = {
        includeInactive: includeInactive === 'true',
        departmentId: departmentId ? parseInt(departmentId) : undefined,
        designationId: designationId ? parseInt(designationId) : undefined,
        search,
        limit: parseInt(limit),
        offset
      };

      // Get staff data and total count
      const [staff, totalCount] = await Promise.all([
        Staff.findAll(options),
        Staff.getCount(options)
      ]);

      const totalPages = Math.ceil(totalCount / parseInt(limit));

      return res.json({
        success: true,
        data: staff,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          limit: parseInt(limit),
          hasNext: parseInt(page) < totalPages,
          hasPrev: parseInt(page) > 1
        }
      });
    } catch (error) {
      console.error('Error fetching staff:', error);
      return res.status(500).json({
        success: false,
        message: 'Error fetching staff records',
        error: error.message
      });
    }
  },

  /**
   * Get staff by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getStaffById: async (req, res) => {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          message: 'Valid staff ID is required'
        });
      }

      const staff = await Staff.findById(parseInt(id));

      if (!staff) {
        return res.status(404).json({
          success: false,
          message: 'Staff member not found'
        });
      }

      return res.json({
        success: true,
        data: staff
      });
    } catch (error) {
      console.error('Error fetching staff by ID:', error);
      return res.status(500).json({
        success: false,
        message: 'Error fetching staff record',
        error: error.message
      });
    }
  },

  /**
   * Create new staff member
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createStaff: async (req, res) => {
    try {
      const staffData = req.body;

      // Validate required fields
      const validationResult = StaffService.validateStaffData(staffData);
      if (!validationResult.isValid) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: validationResult.errors
        });
      }

      // Check for duplicate email
      const existingStaffByEmail = await Staff.findByEmail(staffData.staff_email);
      if (existingStaffByEmail) {
        return res.status(409).json({
          success: false,
          message: 'Staff member with this email already exists'
        });
      }

      // Check for duplicate mobile
      const existingStaffByMobile = await Staff.findByMobile(staffData.staff_mobile);
      if (existingStaffByMobile) {
        return res.status(409).json({
          success: false,
          message: 'Staff member with this mobile number already exists'
        });
      }

      // Check for duplicate Aadhaar if provided
      if (staffData.aadhaar_number) {
        const existingStaffByAadhaar = await Staff.findByAadhaar(staffData.aadhaar_number);
        if (existingStaffByAadhaar) {
          return res.status(409).json({
            success: false,
            message: 'Staff member with this Aadhaar number already exists'
          });
        }
      }

      // Check for duplicate PAN if provided
      if (staffData.pan_number) {
        const existingStaffByPan = await Staff.findByPan(staffData.pan_number);
        if (existingStaffByPan) {
          return res.status(409).json({
            success: false,
            message: 'Staff member with this PAN number already exists'
          });
        }
      }

      // Add created_by from authenticated user
      staffData.created_by = req.user.id;

      // Create staff record
      const newStaff = await Staff.create(staffData);

      return res.status(201).json({
        success: true,
        message: 'Staff member created successfully',
        data: newStaff
      });
    } catch (error) {
      console.error('Error creating staff:', error);
      return res.status(500).json({
        success: false,
        message: 'Error creating staff record',
        error: error.message
      });
    }
  },

  /**
   * Update staff member
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateStaff: async (req, res) => {
    try {
      const { id } = req.params;
      const staffData = req.body;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          message: 'Valid staff ID is required'
        });
      }

      // Check if staff exists
      const existingStaff = await Staff.findById(parseInt(id));
      if (!existingStaff) {
        return res.status(404).json({
          success: false,
          message: 'Staff member not found'
        });
      }

      // Validate updated data
      const validationResult = StaffService.validateStaffData(staffData, true);
      if (!validationResult.isValid) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: validationResult.errors
        });
      }

      // Check for duplicate email if email is being changed
      if (staffData.staff_email && staffData.staff_email !== existingStaff.staff_email) {
        const existingStaffByEmail = await Staff.findByEmail(staffData.staff_email);
        if (existingStaffByEmail) {
          return res.status(409).json({
            success: false,
            message: 'Staff member with this email already exists'
          });
        }
      }

      // Check for duplicate mobile if mobile is being changed
      if (staffData.staff_mobile && staffData.staff_mobile !== existingStaff.staff_mobile) {
        const existingStaffByMobile = await Staff.findByMobile(staffData.staff_mobile);
        if (existingStaffByMobile) {
          return res.status(409).json({
            success: false,
            message: 'Staff member with this mobile number already exists'
          });
        }
      }

      // Check for duplicate Aadhaar if being changed
      if (staffData.aadhaar_number && staffData.aadhaar_number !== existingStaff.aadhaar_number) {
        const existingStaffByAadhaar = await Staff.findByAadhaar(staffData.aadhaar_number);
        if (existingStaffByAadhaar) {
          return res.status(409).json({
            success: false,
            message: 'Staff member with this Aadhaar number already exists'
          });
        }
      }

      // Check for duplicate PAN if being changed
      if (staffData.pan_number && staffData.pan_number !== existingStaff.pan_number) {
        const existingStaffByPan = await Staff.findByPan(staffData.pan_number);
        if (existingStaffByPan) {
          return res.status(409).json({
            success: false,
            message: 'Staff member with this PAN number already exists'
          });
        }
      }

      // Add updated_by from authenticated user
      staffData.updated_by = req.user.id;

      // Update staff record
      const updatedStaff = await Staff.update(parseInt(id), staffData);

      return res.json({
        success: true,
        message: 'Staff member updated successfully',
        data: updatedStaff
      });
    } catch (error) {
      console.error('Error updating staff:', error);
      return res.status(500).json({
        success: false,
        message: 'Error updating staff record',
        error: error.message
      });
    }
  },

  /**
   * Delete staff member
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteStaff: async (req, res) => {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          message: 'Valid staff ID is required'
        });
      }

      // Check if staff exists
      const existingStaff = await Staff.findById(parseInt(id));
      if (!existingStaff) {
        return res.status(404).json({
          success: false,
          message: 'Staff member not found'
        });
      }

      // Delete staff record
      await Staff.delete(parseInt(id));

      return res.json({
        success: true,
        message: 'Staff member deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting staff:', error);
      return res.status(500).json({
        success: false,
        message: 'Error deleting staff record',
        error: error.message
      });
    }
  },

  /**
   * Toggle staff active status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  toggleStaffStatus: async (req, res) => {
    try {
      const { id } = req.params;
      const { is_active } = req.body;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          message: 'Valid staff ID is required'
        });
      }

      if (typeof is_active !== 'boolean') {
        return res.status(400).json({
          success: false,
          message: 'Valid active status is required'
        });
      }

      // Check if staff exists
      const existingStaff = await Staff.findById(parseInt(id));
      if (!existingStaff) {
        return res.status(404).json({
          success: false,
          message: 'Staff member not found'
        });
      }

      // Toggle status
      const result = await Staff.toggleActive(parseInt(id), is_active, req.user.id);

      return res.json({
        success: true,
        message: `Staff member ${is_active ? 'activated' : 'deactivated'} successfully`,
        data: result
      });
    } catch (error) {
      console.error('Error toggling staff status:', error);
      return res.status(500).json({
        success: false,
        message: 'Error updating staff status',
        error: error.message
      });
    }
  },

  /**
   * Bulk delete staff members
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  bulkDeleteStaff: async (req, res) => {
    try {
      const { ids } = req.body;

      if (!Array.isArray(ids) || ids.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Valid array of staff IDs is required'
        });
      }

      // Validate all IDs are numbers
      const validIds = ids.filter(id => !isNaN(parseInt(id))).map(id => parseInt(id));

      if (validIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No valid staff IDs provided'
        });
      }

      // Perform bulk delete
      await Staff.bulkDelete(validIds);

      return res.json({
        success: true,
        message: `${validIds.length} staff members deleted successfully`
      });
    } catch (error) {
      console.error('Error bulk deleting staff:', error);
      return res.status(500).json({
        success: false,
        message: 'Error deleting staff records',
        error: error.message
      });
    }
  },

  /**
   * Get staff by department
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getStaffByDepartment: async (req, res) => {
    try {
      const { departmentId } = req.params;
      const { includeInactive = false } = req.query;

      if (!departmentId || isNaN(parseInt(departmentId))) {
        return res.status(400).json({
          success: false,
          message: 'Valid department ID is required'
        });
      }

      const staff = await Staff.findByDepartment(
        parseInt(departmentId),
        includeInactive === 'true'
      );

      return res.json({
        success: true,
        data: staff
      });
    } catch (error) {
      console.error('Error fetching staff by department:', error);
      return res.status(500).json({
        success: false,
        message: 'Error fetching staff by department',
        error: error.message
      });
    }
  },

  /**
   * Upload profile picture
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  uploadProfilePicture: async (req, res) => {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          message: 'Valid staff ID is required'
        });
      }

      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'Profile picture file is required'
        });
      }

      // Check if staff exists
      const existingStaff = await Staff.findById(parseInt(id));
      if (!existingStaff) {
        return res.status(404).json({
          success: false,
          message: 'Staff member not found'
        });
      }

      // Process file upload using service
      const profilePicturePath = await StaffService.handleProfilePictureUpload(req.file, existingStaff);

      // Update staff record with new profile picture
      const result = await Staff.updateProfilePicture(parseInt(id), profilePicturePath, req.user.id);

      return res.json({
        success: true,
        message: 'Profile picture uploaded successfully',
        data: result
      });
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      return res.status(500).json({
        success: false,
        message: 'Error uploading profile picture',
        error: error.message
      });
    }
  },

  /**
   * Remove profile picture
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  removeProfilePicture: async (req, res) => {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          message: 'Valid staff ID is required'
        });
      }

      // Check if staff exists
      const existingStaff = await Staff.findById(parseInt(id));
      if (!existingStaff) {
        return res.status(404).json({
          success: false,
          message: 'Staff member not found'
        });
      }

      // Remove profile picture file if exists
      if (existingStaff.profile_picture) {
        await StaffService.removeProfilePictureFile(existingStaff.profile_picture);
      }

      // Update staff record
      const result = await Staff.removeProfilePicture(parseInt(id), req.user.id);

      return res.json({
        success: true,
        message: 'Profile picture removed successfully',
        data: result
      });
    } catch (error) {
      console.error('Error removing profile picture:', error);
      return res.status(500).json({
        success: false,
        message: 'Error removing profile picture',
        error: error.message
      });
    }
  }
};

module.exports = StaffController;
