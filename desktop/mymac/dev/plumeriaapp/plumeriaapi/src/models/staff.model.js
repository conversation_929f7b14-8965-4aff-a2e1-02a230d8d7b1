const db = require('../config/database');

/**
 * Staff Model
 * Handles all database operations for staff management
 * @class Staff
 */
const Staff = {
  /**
   * Get all staff with related data and optional filtering
   * @param {Object} options - Query options
   * @param {boolean} options.includeInactive - Include inactive staff
   * @param {number} options.departmentId - Filter by department
   * @param {number} options.designationId - Filter by designation
   * @param {string} options.search - Search term
   * @param {number} options.limit - Pagination limit
   * @param {number} options.offset - Pagination offset
   * @returns {Promise<Array>} Array of staff records
   */
  findAll: async (options = {}) => {
    const {
      includeInactive = false,
      departmentId,
      designationId,
      search,
      limit,
      offset
    } = options;

    let query = `
      SELECT
        s.*,
        bg.name as blood_group_name,
        d.name as designation_name,
        d.code as designation_code,
        dept.name as department_name,
        dept.code as department_code,
        et.name as employment_type_name,
        q.name as qualification_name,
        l.name as locality_name,
        c.name as city_name,
        st.name as state_name,
        r.name as user_role_name,
        u1.username as created_by_username,
        u2.username as updated_by_username
      FROM staffs s
      LEFT JOIN blood_groups bg ON s.blood_group_id = bg.id
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      LEFT JOIN employment_types et ON s.employment_type_id = et.id
      LEFT JOIN qualifications q ON s.qualification_id = q.id
      LEFT JOIN localities l ON s.locality_id = l.id
      LEFT JOIN cities c ON s.city_id = c.id
      LEFT JOIN states st ON s.state_id = st.id
      LEFT JOIN roles r ON s.user_role_id = r.id
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
    `;

    const conditions = [];
    const params = [];

    // Active status filter
    if (!includeInactive) {
      conditions.push('s.is_active = ?');
      params.push(1);
    }

    // Department filter
    if (departmentId) {
      conditions.push('s.department_id = ?');
      params.push(departmentId);
    }

    // Designation filter
    if (designationId) {
      conditions.push('s.designation_id = ?');
      params.push(designationId);
    }

    // Search filter
    if (search) {
      conditions.push(`(
        s.staff_name LIKE ? OR
        s.staff_email LIKE ? OR
        s.staff_mobile LIKE ? OR
        dept.name LIKE ? OR
        d.name LIKE ?
      )`);
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // Add WHERE clause if conditions exist
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    // Add ordering
    query += ' ORDER BY s.staff_name ASC';

    // Add pagination
    if (limit) {
      query += ' LIMIT ?';
      params.push(limit);

      if (offset) {
        query += ' OFFSET ?';
        params.push(offset);
      }
    }

    return await db.query(query, params);
  },

  /**
   * Get total count of staff records
   * @param {Object} options - Filter options
   * @returns {Promise<number>} Total count
   */
  getCount: async (options = {}) => {
    const {
      includeInactive = false,
      departmentId,
      designationId,
      search
    } = options;

    let query = 'SELECT COUNT(*) as total FROM staffs s';
    const conditions = [];
    const params = [];

    if (!includeInactive) {
      conditions.push('s.is_active = ?');
      params.push(1);
    }

    if (departmentId) {
      conditions.push('s.department_id = ?');
      params.push(departmentId);
    }

    if (designationId) {
      conditions.push('s.designation_id = ?');
      params.push(designationId);
    }

    if (search) {
      query += `
        LEFT JOIN departments dept ON s.department_id = dept.id
        LEFT JOIN designations d ON s.designation_id = d.id
      `;
      conditions.push(`(
        s.staff_name LIKE ? OR
        s.staff_email LIKE ? OR
        s.staff_mobile LIKE ? OR
        dept.name LIKE ? OR
        d.name LIKE ?
      )`);
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    const result = await db.query(query, params);
    return result[0].total;
  },

  /**
   * Find staff by ID with all related data
   * @param {number} id - Staff ID
   * @returns {Promise<Object|null>} Staff record or null
   */
  findById: async (id) => {
    const query = `
      SELECT
        s.*,
        bg.name as blood_group_name,
        d.name as designation_name,
        d.code as designation_code,
        dept.name as department_name,
        dept.code as department_code,
        et.name as employment_type_name,
        q.name as qualification_name,
        l.name as locality_name,
        c.name as city_name,
        st.name as state_name,
        r.name as user_role_name,
        u1.username as created_by_username,
        u2.username as updated_by_username
      FROM staffs s
      LEFT JOIN blood_groups bg ON s.blood_group_id = bg.id
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      LEFT JOIN employment_types et ON s.employment_type_id = et.id
      LEFT JOIN qualifications q ON s.qualification_id = q.id
      LEFT JOIN localities l ON s.locality_id = l.id
      LEFT JOIN cities c ON s.city_id = c.id
      LEFT JOIN states st ON s.state_id = st.id
      LEFT JOIN roles r ON s.user_role_id = r.id
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
      WHERE s.id = ?
    `;

    const staff = await db.query(query, [id]);
    return staff[0] || null;
  },

  /**
   * Find staff by email
   * @param {string} email - Staff email
   * @returns {Promise<Object|null>} Staff record or null
   */
  findByEmail: async (email) => {
    const query = 'SELECT * FROM staffs WHERE staff_email = ?';
    const staff = await db.query(query, [email]);
    return staff[0] || null;
  },

  /**
   * Find staff by mobile number
   * @param {string} mobile - Staff mobile number
   * @returns {Promise<Object|null>} Staff record or null
   */
  findByMobile: async (mobile) => {
    const query = 'SELECT * FROM staffs WHERE staff_mobile = ?';
    const staff = await db.query(query, [mobile]);
    return staff[0] || null;
  },

  /**
   * Find staff by Aadhaar number
   * @param {string} aadhaar - Aadhaar number
   * @returns {Promise<Object|null>} Staff record or null
   */
  findByAadhaar: async (aadhaar) => {
    const query = 'SELECT * FROM staffs WHERE aadhaar_number = ?';
    const staff = await db.query(query, [aadhaar]);
    return staff[0] || null;
  },

  /**
   * Find staff by PAN number
   * @param {string} pan - PAN number
   * @returns {Promise<Object|null>} Staff record or null
   */
  findByPan: async (pan) => {
    const query = 'SELECT * FROM staffs WHERE pan_number = ?';
    const staff = await db.query(query, [pan]);
    return staff[0] || null;
  },

  /**
   * Get staff by department
   * @param {number} departmentId - Department ID
   * @param {boolean} includeInactive - Include inactive staff
   * @returns {Promise<Array>} Array of staff records
   */
  findByDepartment: async (departmentId, includeInactive = false) => {
    let query = `
      SELECT s.*, d.name as designation_name, dept.name as department_name
      FROM staffs s
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      WHERE s.department_id = ?
    `;

    const params = [departmentId];

    if (!includeInactive) {
      query += ' AND s.is_active = ?';
      params.push(1);
    }

    query += ' ORDER BY s.staff_name ASC';

    return await db.query(query, params);
  },

  /**
   * Create new staff record
   * @param {Object} staffData - Staff data
   * @returns {Promise<Object>} Created staff record
   */
  create: async (staffData) => {
    const {
      staff_name,
      staff_mobile,
      staff_email,
      gender,
      user_role_id,
      date_of_birth,
      marital_status,
      blood_group_id,
      joining_date,
      emergency_contact_name,
      emergency_contact_phone,
      emergency_contact_relation,
      designation_id,
      employment_type_id,
      health_insurance_provider,
      health_insurance_number,
      department_id,
      salary_amount,
      salary_last_hiked_date,
      staff_address,
      locality_id,
      city_id,
      pincode,
      state_id,
      aadhaar_number,
      pan_number,
      qualification_id,
      bank_name,
      account_number,
      ifsc_code,
      profile_picture,
      is_active = 1,
      created_by
    } = staffData;

    const query = `
      INSERT INTO staffs (
        staff_name, staff_mobile, staff_email, gender, user_role_id,
        date_of_birth, marital_status, blood_group_id, joining_date,
        emergency_contact_name, emergency_contact_phone, emergency_contact_relation,
        designation_id, employment_type_id, health_insurance_provider,
        health_insurance_number, department_id, salary_amount, salary_last_hiked_date,
        staff_address, locality_id, city_id, pincode, state_id,
        aadhaar_number, pan_number, qualification_id, bank_name,
        account_number, ifsc_code, profile_picture, is_active, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      staff_name, staff_mobile, staff_email, gender, user_role_id,
      date_of_birth, marital_status, blood_group_id, joining_date,
      emergency_contact_name, emergency_contact_phone, emergency_contact_relation,
      designation_id, employment_type_id, health_insurance_provider,
      health_insurance_number, department_id, salary_amount, salary_last_hiked_date,
      staff_address, locality_id, city_id, pincode, state_id,
      aadhaar_number, pan_number, qualification_id, bank_name,
      account_number, ifsc_code, profile_picture, is_active, created_by
    ];

    const result = await db.query(query, params);
    return { id: result.insertId, ...staffData };
  },

  /**
   * Update staff record
   * @param {number} id - Staff ID
   * @param {Object} staffData - Updated staff data
   * @returns {Promise<Object>} Updated staff record
   */
  update: async (id, staffData) => {
    const updateFields = [];
    const params = [];

    // Build dynamic update query
    Object.keys(staffData).forEach(key => {
      if (staffData[key] !== undefined && key !== 'id') {
        updateFields.push(`${key} = ?`);
        params.push(staffData[key]);
      }
    });

    if (updateFields.length === 0) {
      throw new Error('No fields to update');
    }

    const query = `UPDATE staffs SET ${updateFields.join(', ')} WHERE id = ?`;
    params.push(id);

    await db.query(query, params);
    return { id, ...staffData };
  },

  /**
   * Delete staff record
   * @param {number} id - Staff ID
   * @returns {Promise<Object>} Delete result
   */
  delete: async (id) => {
    const query = 'DELETE FROM staffs WHERE id = ?';
    return await db.query(query, [id]);
  },

  /**
   * Bulk delete staff records
   * @param {Array<number>} ids - Array of staff IDs
   * @returns {Promise<Object>} Delete result
   */
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid staff IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM staffs WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  /**
   * Toggle staff active status
   * @param {number} id - Staff ID
   * @param {boolean} isActive - New active status
   * @param {number} updatedBy - User ID who updated
   * @returns {Promise<Object>} Update result
   */
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE staffs SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  },

  /**
   * Update profile picture
   * @param {number} id - Staff ID
   * @param {string} profilePicture - Profile picture path
   * @param {number} updatedBy - User ID who updated
   * @returns {Promise<Object>} Update result
   */
  updateProfilePicture: async (id, profilePicture, updatedBy) => {
    const query = 'UPDATE staffs SET profile_picture = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [profilePicture, updatedBy, id]);
    return { id, profile_picture: profilePicture };
  },

  /**
   * Remove profile picture
   * @param {number} id - Staff ID
   * @param {number} updatedBy - User ID who updated
   * @returns {Promise<Object>} Update result
   */
  removeProfilePicture: async (id, updatedBy) => {
    const query = 'UPDATE staffs SET profile_picture = NULL, updated_by = ? WHERE id = ?';
    await db.query(query, [updatedBy, id]);
    return { id, profile_picture: null };
  }
};

module.exports = Staff;
