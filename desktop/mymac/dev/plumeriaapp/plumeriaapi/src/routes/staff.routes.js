const express = require('express');
const StaffController = require('../controllers/staff.controller');
const { verifyToken, hasModulePermission } = require('../middleware/auth.middleware');
const { uploadProfilePicture } = require('../middleware/upload.middleware');

const router = express.Router();

/**
 * Staff Management Routes
 * All routes require authentication and appropriate permissions
 */

// Apply authentication middleware to all routes
router.use(verifyToken);

/**
 * @route   GET /api/staffs
 * @desc    Get all staff with pagination and filtering
 * @access  Private (requires 'staff' module 'read' permission)
 * @query   {number} page - Page number (default: 1)
 * @query   {number} limit - Items per page (default: 10)
 * @query   {string} search - Search term
 * @query   {number} departmentId - Filter by department
 * @query   {number} designationId - Filter by designation
 * @query   {boolean} includeInactive - Include inactive staff
 */
router.get('/',
  hasModulePermission('staff', 'read'),
  StaffController.getAllStaff
);

/**
 * @route   GET /api/staffs/:id
 * @desc    Get staff by ID
 * @access  Private (requires 'staff' module 'read' permission)
 * @param   {number} id - Staff ID
 */
router.get('/:id',
  hasModulePermission('staff', 'read'),
  StaffController.getStaffById
);

/**
 * @route   POST /api/staffs
 * @desc    Create new staff member
 * @access  Private (requires 'staff' module 'create' permission)
 * @body    {Object} staffData - Staff information
 */
router.post('/',
  hasModulePermission('staff', 'create'),
  StaffController.createStaff
);

/**
 * @route   PUT /api/staffs/:id
 * @desc    Update staff member
 * @access  Private (requires 'staff' module 'update' permission)
 * @param   {number} id - Staff ID
 * @body    {Object} staffData - Updated staff information
 */
router.put('/:id',
  hasModulePermission('staff', 'update'),
  StaffController.updateStaff
);

/**
 * @route   DELETE /api/staffs/:id
 * @desc    Delete staff member
 * @access  Private (requires 'staff' module 'delete' permission)
 * @param   {number} id - Staff ID
 */
router.delete('/:id',
  hasModulePermission('staff', 'delete'),
  StaffController.deleteStaff
);

/**
 * @route   PATCH /api/staffs/:id/status
 * @desc    Toggle staff active status
 * @access  Private (requires 'staff' module 'update' permission)
 * @param   {number} id - Staff ID
 * @body    {boolean} is_active - New active status
 */
router.patch('/:id/status',
  hasModulePermission('staff', 'update'),
  StaffController.toggleStaffStatus
);

/**
 * @route   POST /api/staffs/bulk-delete
 * @desc    Bulk delete staff members
 * @access  Private (requires 'staff' module 'delete' permission)
 * @body    {Array<number>} ids - Array of staff IDs to delete
 */
router.post('/bulk-delete',
  hasModulePermission('staff', 'delete'),
  StaffController.bulkDeleteStaff
);

/**
 * @route   GET /api/staffs/department/:departmentId
 * @desc    Get staff by department
 * @access  Private (requires 'staff' module 'read' permission)
 * @param   {number} departmentId - Department ID
 * @query   {boolean} includeInactive - Include inactive staff
 */
router.get('/department/:departmentId',
  hasModulePermission('staff', 'read'),
  StaffController.getStaffByDepartment
);

/**
 * @route   POST /api/staffs/:id/upload-photo
 * @desc    Upload profile picture for staff member
 * @access  Private (requires 'staff' module 'update' permission)
 * @param   {number} id - Staff ID
 * @body    {File} profile_picture - Profile picture file (multipart/form-data)
 */
router.post('/:id/upload-photo',
  hasModulePermission('staff', 'update'),
  uploadProfilePicture,
  StaffController.uploadProfilePicture
);

/**
 * @route   DELETE /api/staffs/:id/photo
 * @desc    Remove profile picture for staff member
 * @access  Private (requires 'staff' module 'update' permission)
 * @param   {number} id - Staff ID
 */
router.delete('/:id/photo',
  hasModulePermission('staff', 'update'),
  StaffController.removeProfilePicture
);

/**
 * Error handling middleware for this router
 */
router.use((error, req, res, next) => {
  console.error('Staff routes error:', error);

  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      error: error.message
    });
  }

  if (error.name === 'CastError') {
    return res.status(400).json({
      success: false,
      message: 'Invalid ID format',
      error: error.message
    });
  }

  return res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: error.message
  });
});

module.exports = router;
